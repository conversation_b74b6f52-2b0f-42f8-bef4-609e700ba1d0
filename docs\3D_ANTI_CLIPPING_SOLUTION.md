# 3D地图对象防裁剪解决方案

## 问题描述

在3D地图中，当俯仰角较低（视角接近水平）时，如果球体与地面贴合或高度设置过低，可能会被裁剪掉，导致对象不可见。

## 解决方案

参考高德地图官方GLCustomLayer示例：https://lbs.amap.com/demo/javascript-api-v2/example/selflayer/glcustom-layer

### 1. 相机参数设置（按照官方示例）

```javascript
// 创建透视相机
this.camera = new THREE.PerspectiveCamera(
  60,
  window.innerWidth / window.innerHeight,
  100, // 官方示例使用100作为near值
  1 << 30
)
```

### 2. 渲染器配置（按照官方示例）

```javascript
// 创建渲染器
this.renderer = new THREE.WebGLRenderer({
  context: gl
  // 不设置alpha和antialias，按照官方示例
})
this.renderer.autoClear = false
```

### 3. 渲染流程（按照官方示例）

```javascript
render: () => {
  // 重新设置 three 的 gl 上下文状态
  renderer.resetState()

  // 重新设置图层的渲染中心点
  customCoords.setCenter([116.52, 39.79])

  // 获取相机参数并设置
  var { near, far, fov, up, lookAt, position } = customCoords.getCameraParams()
  camera.near = near
  camera.far = far
  camera.fov = fov
  camera.position.set(...position)
  camera.up.set(...up)
  camera.lookAt(...lookAt)
  camera.updateProjectionMatrix()

  // 渲染场景
  renderer.render(scene, camera)

  // 重新设置 three 的 gl 上下文状态
  renderer.resetState()
}
```

### 4. 对象高度设置

```javascript
// 设置最小500米高度，确保对象在地面上方
const objectHeight = Math.max(waypoint.height, 500)
```

## 修改的文件

### 1. ThreeWaypointManager.js
- 相机near值改为100（官方示例值）
- 渲染器配置简化，去掉alpha和antialias
- 添加customCoords.setCenter()调用
- 对象最小高度设为500米

### 2. ThreeFlightPathManager.js
- 相同的相机和渲染器配置修改
- 相同的渲染流程优化

### 3. flightAirline.vue
- 球体默认高度从100米改为500米

## 关键改进点

1. **相机near值**: 使用官方示例的100而不是0.1
2. **渲染中心**: 每次渲染前调用setCenter()
3. **对象高度**: 确保最小500米高度
4. **渲染流程**: 严格按照官方示例的resetState()流程

## 测试方法

1. 打开飞控界面
2. 切换到3D视角
3. 调整俯仰角到接近0度（水平视角）
4. 点击地图添加球体标记
5. 观察球体是否在水平视角下仍然可见

## 预期效果

- ✅ 在任何俯仰角下，3D对象都保持可见
- ✅ 不会出现对象突然消失或闪烁
- ✅ 渲染性能保持稳定
- ✅ 深度关系正确，无Z-fighting问题
